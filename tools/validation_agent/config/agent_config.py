"""
Configuration and prompts for the LangGraph AI validation agent.
"""

import os
from typing import Dict, List
from dataclasses import dataclass


@dataclass
class AgentConfig:
    """Configuration for the validation agent."""
    
    # LLM Configuration
    model_name: str = "gemini-2.0-flash-exp"
    temperature: float = 0.1
    max_tokens: int = 4096
    
    # Database Configuration
    pinecone_namespace: str = "vlaamse_codex"
    neo4j_timeout: int = 30
    
    # Validation Thresholds
    min_semantic_relevance: float = 0.7
    max_response_time: float = 5.0
    min_results_count: int = 1
    
    # Performance Targets
    target_response_time: float = 2.0
    target_accuracy: float = 0.85
    target_consistency: float = 0.95


class PromptTemplates:
    """Prompt templates for the validation agent."""
    
    SYSTEM_PROMPT = """You are a legal document validation specialist for the Belgian/Flemish legal system.
Your role is to validate that ingested legal documents are properly processed and searchable.

CAPABILITIES:
- Query Pinecone vector database for semantic document search
- Query Neo4j knowledge graph for structural relationships
- Cross-validate results between databases
- Generate validation reports with actionable insights

CONTEXT:
- Documents are from the Vlaamse Codex (Flemish legal code)
- Content is in Dutch with some French legal terminology
- Focus on acts (wetten/decreten) and their articles (artikelen)
- Validate both semantic meaning and structural relationships

VALIDATION CRITERIA:
- Semantic relevance of search results
- Structural integrity of act-article relationships
- Consistency between vector embeddings and knowledge graph
- Quality of Dutch legal text processing

Always provide specific, actionable feedback about data quality and system performance."""

    SEMANTIC_SEARCH_PROMPT = """Analyze this legal query and determine the best approach for validation:

Query: {user_query}

Tasks:
1. Identify key legal concepts and terminology
2. Determine appropriate search strategy (semantic vs. structural)
3. Suggest validation criteria for results
4. Predict expected result characteristics

Respond with a structured analysis including:
- Search strategy recommendation
- Key terms to validate in results
- Expected result types (acts, articles, etc.)
- Quality criteria for validation"""

    KNOWLEDGE_GRAPH_PROMPT = """Convert this legal query into appropriate Neo4j validation queries:

Query: {user_query}

Generate Cypher queries to:
1. Find relevant entities (acts, articles)
2. Validate relationships and structure
3. Check data completeness and integrity
4. Gather statistics for validation

Provide queries that will help validate:
- Entity existence and completeness
- Relationship integrity
- Data quality metrics
- Cross-reference capabilities"""

    CROSS_VALIDATION_PROMPT = """Compare and validate results from Pinecone and Neo4j:

Pinecone Results: {pinecone_results}
Neo4j Results: {neo4j_results}

Validation Tasks:
1. Check consistency between vector embeddings and knowledge graph entities
2. Identify any missing or inconsistent data
3. Validate that semantic search results have corresponding structured data
4. Assess overall data quality and completeness

Provide a detailed analysis including:
- Consistency score (0-1)
- Specific inconsistencies found
- Data quality assessment
- Recommendations for improvement"""

    REPORT_GENERATION_PROMPT = """Generate a comprehensive validation report based on the analysis:

Validation Results: {validation_results}

Create a structured report including:
1. Executive Summary (overall validation status)
2. Semantic Search Performance (accuracy, relevance, speed)
3. Knowledge Graph Integrity (completeness, relationships, structure)
4. Cross-Database Consistency (alignment, discrepancies)
5. Recommendations (immediate actions, improvements)

Format the report for technical stakeholders with specific metrics and actionable insights."""


class ValidationQueries:
    """Predefined validation queries for testing."""
    
    SEMANTIC_QUERIES = [
        "Zoek documenten over arbeidsrecht en werknemersrechten",
        "Find legislation about employment contracts and worker protection",
        "What are the pension rights for government employees?",
        "Toon wetgeving over economische ontwikkeling in Vlaanderen",
        "Find acts related to business subsidies and economic incentives",
        "What laws govern small business support in the Flemish region?",
        "Zoek artikelen over overheidsorganisatie en bestuur",
        "Find documents about municipal governance and local administration",
        "What regulations exist for public sector employment?"
    ]
    
    STRUCTURAL_QUERIES = [
        "Show all articles from act vlaamse_1994035964",
        "Find relationships between employment and pension laws",
        "List all acts from 1994 with their article counts",
        "Show the structure of act vlaamse_1970120119",
        "Find acts that reference economic development"
    ]
    
    CROSS_VALIDATION_SCENARIOS = [
        {
            "description": "Verify vector embeddings match Neo4j entities",
            "semantic_query": "employment law articles",
            "structural_query": "MATCH (art:Article) WHERE art.text CONTAINS 'employment' RETURN art.id, art.heading",
            "validation_criteria": "Check if Pinecone results correspond to Neo4j articles"
        },
        {
            "description": "Validate act-article relationships",
            "semantic_query": "pension benefits legislation",
            "structural_query": "MATCH (a:Act)-[:HAS_ARTICLE]->(art:Article) WHERE a.title CONTAINS 'pension' RETURN a.id, art.id",
            "validation_criteria": "Ensure semantic results include proper act-article relationships"
        }
    ]


# Environment variable validation
def validate_environment():
    """Validate that required environment variables are set."""
    required_vars = [
        "GEMINI_API_KEY",
        "PINECONE_API_KEY",
        "PINECONE_INDEX",
        "NEO4J_URI",
        "NEO4J_USERNAME",
        "NEO4J_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return True
