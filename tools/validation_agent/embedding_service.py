"""
Embedding service for the validation agent using Voyage AI.
Provides real embeddings for Dutch legal text processing.
"""

import os
import re
import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

import voyageai
from voyageai import AsyncClient

logger = logging.getLogger(__name__)


@dataclass
class EmbeddingResult:
    """Result from embedding generation."""
    text: str
    embedding: List[float]
    model: str
    dimension: int


class DutchLegalTextPreprocessor:
    """Preprocessor for Dutch legal text to improve embedding quality."""
    
    def __init__(self):
        # Common Dutch legal abbreviations and their expansions
        self.legal_abbreviations = {
            'art.': 'artikel',
            'Art.': 'Artikel',
            'wet': 'wetgeving',
            'decreet': 'decreet',
            'besluit': 'besluit',
            'KB': 'Koninklijk Besluit',
            'VR': 'Vlaamse Regering',
            'BS': 'Bel<PERSON>ch Staatsblad',
            'MB': '<PERSON><PERSON><PERSON> Be<PERSON>luit'
        }
        
        # Legal domain terms that should be preserved
        self.legal_terms = {
            'milieubescherming', 'natuurbehoud', 'ruimtelijke ordening',
            'stedenbouw', 'vergunning', 'onteigening', 'erfrecht',
            'handelsrecht', 'arbeidsrecht', 'strafrecht', 'burgerlijk recht'
        }
    
    def preprocess(self, text: str) -> str:
        """
        Preprocess Dutch legal text for better embedding quality.
        
        Args:
            text: Raw Dutch legal text
            
        Returns:
            Preprocessed text optimized for embeddings
        """
        if not text:
            return ""
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Expand common legal abbreviations
        for abbrev, expansion in self.legal_abbreviations.items():
            text = text.replace(abbrev, expansion)
        
        # Remove excessive punctuation but preserve sentence structure
        text = re.sub(r'[.]{2,}', '.', text)
        text = re.sub(r'[,]{2,}', ',', text)
        
        # Normalize article references
        text = re.sub(r'artikel\s+(\d+)', r'artikel \1', text, flags=re.IGNORECASE)
        
        # Remove HTML/XML tags if present
        text = re.sub(r'<[^>]+>', '', text)
        
        # Normalize quotes
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        
        return text.strip()
    
    def extract_key_terms(self, text: str) -> List[str]:
        """Extract key legal terms from text for enhanced search."""
        text_lower = text.lower()
        found_terms = []
        
        for term in self.legal_terms:
            if term in text_lower:
                found_terms.append(term)
        
        # Extract article numbers
        article_matches = re.findall(r'artikel\s+(\d+)', text_lower)
        found_terms.extend([f"artikel {num}" for num in article_matches])
        
        return found_terms


class VoyageEmbeddingService:
    """Service for generating embeddings using Voyage AI."""
    
    def __init__(self, model: str = "voyage-3-large"):
        """
        Initialize the embedding service.
        
        Args:
            model: Voyage AI model to use for embeddings
        """
        self.model = model
        self.api_key = os.getenv('VOYAGE_API_KEY')
        
        if not self.api_key:
            raise ValueError("VOYAGE_API_KEY environment variable is required")
        
        # Initialize clients
        self.client = voyageai.Client(api_key=self.api_key)
        self.async_client = AsyncClient(api_key=self.api_key)
        
        # Initialize preprocessor
        self.preprocessor = DutchLegalTextPreprocessor()
        
        # Cache for embeddings (in production, use Redis or similar)
        self._embedding_cache: Dict[str, List[float]] = {}
        
        logger.info(f"VoyageEmbeddingService initialized with model: {model}")
    
    def _get_cache_key(self, text: str, input_type: str) -> str:
        """Generate cache key for embedding."""
        import hashlib
        content = f"{text}:{input_type}:{self.model}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def embed_query(self, query: str) -> EmbeddingResult:
        """
        Generate embedding for a search query.
        
        Args:
            query: Search query text in Dutch
            
        Returns:
            EmbeddingResult with the query embedding
        """
        # Preprocess the query
        processed_query = self.preprocessor.preprocess(query)
        
        # Check cache
        cache_key = self._get_cache_key(processed_query, "query")
        if cache_key in self._embedding_cache:
            logger.debug(f"Using cached embedding for query: {query[:50]}...")
            return EmbeddingResult(
                text=processed_query,
                embedding=self._embedding_cache[cache_key],
                model=self.model,
                dimension=len(self._embedding_cache[cache_key])
            )
        
        try:
            # Generate embedding using Voyage AI
            result = await self.async_client.embed(
                texts=[processed_query],
                model=self.model,
                input_type="query"
            )
            
            embedding = result.embeddings[0]
            
            # Cache the result
            self._embedding_cache[cache_key] = embedding
            
            logger.info(f"Generated query embedding: {len(embedding)} dimensions")
            
            return EmbeddingResult(
                text=processed_query,
                embedding=embedding,
                model=self.model,
                dimension=len(embedding)
            )
            
        except Exception as e:
            logger.error(f"Failed to generate query embedding: {e}")
            raise
    
    async def embed_document(self, document: str) -> EmbeddingResult:
        """
        Generate embedding for a document.
        
        Args:
            document: Document text in Dutch
            
        Returns:
            EmbeddingResult with the document embedding
        """
        # Preprocess the document
        processed_doc = self.preprocessor.preprocess(document)
        
        # Check cache
        cache_key = self._get_cache_key(processed_doc, "document")
        if cache_key in self._embedding_cache:
            logger.debug(f"Using cached embedding for document: {document[:50]}...")
            return EmbeddingResult(
                text=processed_doc,
                embedding=self._embedding_cache[cache_key],
                model=self.model,
                dimension=len(self._embedding_cache[cache_key])
            )
        
        try:
            # Generate embedding using Voyage AI
            result = await self.async_client.embed(
                texts=[processed_doc],
                model=self.model,
                input_type="document"
            )
            
            embedding = result.embeddings[0]
            
            # Cache the result
            self._embedding_cache[cache_key] = embedding
            
            logger.info(f"Generated document embedding: {len(embedding)} dimensions")
            
            return EmbeddingResult(
                text=processed_doc,
                embedding=embedding,
                model=self.model,
                dimension=len(embedding)
            )
            
        except Exception as e:
            logger.error(f"Failed to generate document embedding: {e}")
            raise
    
    async def embed_batch(
        self, 
        texts: List[str], 
        input_type: str = "document"
    ) -> List[EmbeddingResult]:
        """
        Generate embeddings for a batch of texts.
        
        Args:
            texts: List of texts to embed
            input_type: Type of input ("query" or "document")
            
        Returns:
            List of EmbeddingResult objects
        """
        if not texts:
            return []
        
        # Preprocess all texts
        processed_texts = [self.preprocessor.preprocess(text) for text in texts]
        
        # Check cache for each text
        results = []
        uncached_texts = []
        uncached_indices = []
        
        for i, text in enumerate(processed_texts):
            cache_key = self._get_cache_key(text, input_type)
            if cache_key in self._embedding_cache:
                results.append(EmbeddingResult(
                    text=text,
                    embedding=self._embedding_cache[cache_key],
                    model=self.model,
                    dimension=len(self._embedding_cache[cache_key])
                ))
            else:
                uncached_texts.append(text)
                uncached_indices.append(i)
                results.append(None)  # Placeholder
        
        # Generate embeddings for uncached texts
        if uncached_texts:
            try:
                batch_result = await self.async_client.embed(
                    texts=uncached_texts,
                    model=self.model,
                    input_type=input_type
                )
                
                # Cache and store results
                for i, (text, embedding) in enumerate(zip(uncached_texts, batch_result.embeddings)):
                    cache_key = self._get_cache_key(text, input_type)
                    self._embedding_cache[cache_key] = embedding
                    
                    result_index = uncached_indices[i]
                    results[result_index] = EmbeddingResult(
                        text=text,
                        embedding=embedding,
                        model=self.model,
                        dimension=len(embedding)
                    )
                
                logger.info(f"Generated {len(uncached_texts)} new embeddings in batch")
                
            except Exception as e:
                logger.error(f"Failed to generate batch embeddings: {e}")
                raise
        
        return results
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get embedding cache statistics."""
        return {
            'cache_size': len(self._embedding_cache),
            'model': self.model,
            'preprocessor_terms': len(self.preprocessor.legal_terms)
        }
    
    def clear_cache(self):
        """Clear the embedding cache."""
        self._embedding_cache.clear()
        logger.info("Embedding cache cleared")


# Global instance for easy access
_embedding_service: Optional[VoyageEmbeddingService] = None


def get_embedding_service() -> VoyageEmbeddingService:
    """Get the global embedding service instance."""
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = VoyageEmbeddingService()
    return _embedding_service
