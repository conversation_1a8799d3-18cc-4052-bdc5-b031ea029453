#!/usr/bin/env python3
"""
Validation runner script for the LangGraph AI validation agent.

Usage:
    python tools/validation_agent/run_validation.py --query "Find employment law documents"
    python tools/validation_agent/run_validation.py --comprehensive
    python tools/validation_agent/run_validation.py --test-connections
"""

import asyncio
import argparse
import json
import logging
import sys
import os
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / '.env')

from tools.validation_agent.agent import ValidationAgent
from tools.validation_agent.config.agent_config import AgentConfig, ValidationQueries

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Rich console for beautiful output
console = Console()


async def test_database_connections():
    """Test database connections."""
    console.print("\n🔍 Testing Database Connections", style="bold blue")
    
    try:
        agent = ValidationAgent()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # Test Pinecone
            task1 = progress.add_task("Testing Pinecone connection...", total=None)
            try:
                stats = agent.pinecone_connector.get_index_stats()
                progress.update(task1, description="✅ Pinecone connected")
                console.print(f"  📊 Total vectors: {stats['total_vectors']}")
                console.print(f"  📊 Vlaamse vectors: {stats['vlaamse_vectors']}")
            except Exception as e:
                progress.update(task1, description="❌ Pinecone failed")
                console.print(f"  Error: {e}", style="red")
            
            # Test Neo4j
            task2 = progress.add_task("Testing Neo4j connection...", total=None)
            try:
                stats = await agent.neo4j_connector.get_entity_statistics()
                progress.update(task2, description="✅ Neo4j connected")
                console.print(f"  📊 Sample acts: {len(stats['sample_acts'])}")
                console.print(f"  📊 Articles stats: {stats['articles_statistics']}")
            except Exception as e:
                progress.update(task2, description="❌ Neo4j failed")
                console.print(f"  Error: {e}", style="red")
        
        agent.close()
        console.print("\n✅ Database connection test completed", style="green")
        
    except Exception as e:
        console.print(f"\n❌ Connection test failed: {e}", style="red")


async def run_single_query(query: str):
    """Run validation for a single query."""
    console.print(f"\n🤖 Running Validation Query", style="bold blue")
    console.print(f"Query: {query}", style="italic")
    
    try:
        agent = ValidationAgent()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Processing query...", total=None)
            
            result = await agent.validate_query(query)
            progress.update(task, description="✅ Query completed")
        
        # Display results
        if result['success']:
            console.print("\n✅ Validation Successful", style="green")
            
            # Results table
            table = Table(title="Validation Results")
            table.add_column("Database", style="cyan")
            table.add_column("Results Count", justify="right")
            table.add_column("Sample Results", style="magenta")
            
            # Pinecone results
            pinecone_sample = ""
            if result['pinecone_results']:
                sample = result['pinecone_results'][0]
                pinecone_sample = f"{sample.get('title', 'N/A')[:40]}..."
            
            table.add_row(
                "Pinecone",
                str(len(result['pinecone_results'])),
                pinecone_sample
            )
            
            # Neo4j results
            neo4j_sample = ""
            if result['neo4j_results']:
                sample = result['neo4j_results'][0]
                neo4j_sample = f"{sample.get('a.title', sample.get('act_title', 'N/A'))[:40]}..."
            
            table.add_row(
                "Neo4j",
                str(len(result['neo4j_results'])),
                neo4j_sample
            )
            
            console.print(table)
            
            # Validation metrics
            if result['validation_metrics']:
                metrics = result['validation_metrics']
                console.print(f"\n📊 Consistency Score: {metrics.get('consistency_score', 0):.2f}")
                console.print(f"📊 Overlapping Entities: {metrics.get('overlapping_entities', 0)}")
            
            # AI Report
            if result['report']:
                console.print(Panel(result['report'], title="AI Validation Report", border_style="green"))
        
        else:
            console.print("\n❌ Validation Failed", style="red")
            for error in result['errors']:
                console.print(f"  • {error}", style="red")
        
        agent.close()
        
    except Exception as e:
        console.print(f"\n❌ Validation failed: {e}", style="red")


async def run_comprehensive_validation():
    """Run comprehensive validation with predefined queries."""
    console.print("\n🚀 Running Comprehensive Validation", style="bold blue")
    
    try:
        agent = ValidationAgent()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Running comprehensive validation...", total=None)
            
            results = await agent.run_comprehensive_validation()
            progress.update(task, description="✅ Comprehensive validation completed")
        
        # Display summary
        metrics = results['overall_metrics']
        console.print(f"\n📊 Validation Summary:")
        console.print(f"  Total Queries: {metrics['total_queries']}")
        console.print(f"  Successful: {metrics['successful_queries']}")
        console.print(f"  Success Rate: {metrics['success_rate']:.1%}")
        console.print(f"  Average Consistency: {metrics['average_consistency']:.2f}")
        
        # Detailed results table
        table = Table(title="Detailed Results")
        table.add_column("Query Type", style="cyan")
        table.add_column("Query", style="white")
        table.add_column("Status", justify="center")
        table.add_column("Pinecone", justify="right")
        table.add_column("Neo4j", justify="right")
        table.add_column("Consistency", justify="right")
        
        # Add semantic queries
        for result in results['semantic_queries']:
            status = "✅" if result['success'] else "❌"
            consistency = result.get('validation_metrics', {}).get('consistency_score', 0)
            
            table.add_row(
                "Semantic",
                result['query'][:30] + "...",
                status,
                str(len(result.get('pinecone_results', []))),
                str(len(result.get('neo4j_results', []))),
                f"{consistency:.2f}"
            )
        
        # Add structural queries
        for result in results['structural_queries']:
            status = "✅" if result['success'] else "❌"
            consistency = result.get('validation_metrics', {}).get('consistency_score', 0)
            
            table.add_row(
                "Structural",
                result['query'][:30] + "...",
                status,
                str(len(result.get('pinecone_results', []))),
                str(len(result.get('neo4j_results', []))),
                f"{consistency:.2f}"
            )
        
        console.print(table)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"validation_report_{timestamp}.json"
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        console.print(f"\n💾 Detailed results saved to: {output_file}")
        
        agent.close()
        
    except Exception as e:
        console.print(f"\n❌ Comprehensive validation failed: {e}", style="red")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="LangGraph AI Validation Agent")
    parser.add_argument("--query", "-q", help="Single query to validate")
    parser.add_argument("--comprehensive", "-c", action="store_true", help="Run comprehensive validation")
    parser.add_argument("--test-connections", "-t", action="store_true", help="Test database connections")
    
    args = parser.parse_args()
    
    console.print("🤖 LangGraph AI Validation Agent", style="bold green")
    console.print("Powered by Google Gemini 2.0 Flash", style="italic")
    
    if args.test_connections:
        asyncio.run(test_database_connections())
    elif args.query:
        asyncio.run(run_single_query(args.query))
    elif args.comprehensive:
        asyncio.run(run_comprehensive_validation())
    else:
        console.print("\nUsage examples:", style="yellow")
        console.print("  python tools/validation_agent/run_validation.py --test-connections")
        console.print("  python tools/validation_agent/run_validation.py --query 'Find employment law documents'")
        console.print("  python tools/validation_agent/run_validation.py --comprehensive")


if __name__ == "__main__":
    main()
