"""
Neo4j connector for knowledge graph validation.
"""

import os
import time
import asyncio
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from neo4j import GraphDatabase
import logging

logger = logging.getLogger(__name__)


@dataclass
class Neo4jResult:
    """Represents a Neo4j query result."""
    data: Dict[str, Any]
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get value from result data."""
        return self.data.get(key, default)


class Neo4jConnector:
    """Connector for Neo4j knowledge graph operations."""
    
    def __init__(self, timeout: int = 30):
        """Initialize Neo4j connector."""
        self.driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USERNAME'), os.getenv('NEO4J_PASSWORD'))
        )
        self.timeout = timeout
        
        # Validate connection
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                if test_value == 1:
                    logger.info("Successfully connected to Neo4j")
                else:
                    raise Exception("Connection test failed")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
    
    async def execute_cypher(
        self, 
        query: str, 
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[Neo4jResult]:
        """
        Execute a Cypher query asynchronously.
        
        Args:
            query: Cypher query string
            parameters: Query parameters
            
        Returns:
            List of Neo4jResult objects
        """
        start_time = time.time()
        
        try:
            # Run in thread pool since neo4j driver is synchronous
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                None, 
                self._execute_cypher_sync, 
                query, 
                parameters or {}
            )
            
            response_time = time.time() - start_time
            logger.info(f"Cypher query completed in {response_time:.2f}s, returned {len(results)} results")
            
            return results
            
        except Exception as e:
            logger.error(f"Cypher query failed: {e}")
            raise
    
    def _execute_cypher_sync(self, query: str, parameters: Dict[str, Any]) -> List[Neo4jResult]:
        """Execute Cypher query synchronously."""
        with self.driver.session() as session:
            result = session.run(query, parameters)
            return [Neo4jResult(data=record.data()) for record in result]
    
    async def validate_relationships(self) -> Dict[str, Any]:
        """
        Validate act-article relationships integrity.
        
        Returns:
            Dictionary with relationship validation metrics
        """
        try:
            # Count total acts and articles
            acts_query = "MATCH (a:Act) WHERE a.source = 'vlaamse' RETURN count(a) as count"
            acts_result = await self.execute_cypher(acts_query)
            total_acts = acts_result[0].get('count', 0) if acts_result else 0
            
            articles_query = "MATCH (art:Article) WHERE art.id STARTS WITH 'vlaamse_' RETURN count(art) as count"
            articles_result = await self.execute_cypher(articles_query)
            total_articles = articles_result[0].get('count', 0) if articles_result else 0
            
            # Count relationships
            relationships_query = """
                MATCH (a:Act)-[r:HAS_ARTICLE]->(art:Article) 
                WHERE a.source = 'vlaamse' 
                RETURN count(r) as count
            """
            rel_result = await self.execute_cypher(relationships_query)
            total_relationships = rel_result[0].get('count', 0) if rel_result else 0
            
            # Check for orphaned articles (articles without acts)
            orphaned_query = """
                MATCH (art:Article) 
                WHERE art.id STARTS WITH 'vlaamse_' 
                AND NOT EXISTS((a:Act)-[:HAS_ARTICLE]->(art))
                RETURN count(art) as count
            """
            orphaned_result = await self.execute_cypher(orphaned_query)
            orphaned_articles = orphaned_result[0].get('count', 0) if orphaned_result else 0
            
            # Check for acts without articles
            empty_acts_query = """
                MATCH (a:Act) 
                WHERE a.source = 'vlaamse' 
                AND NOT EXISTS((a)-[:HAS_ARTICLE]->(:Article))
                RETURN count(a) as count
            """
            empty_acts_result = await self.execute_cypher(empty_acts_query)
            empty_acts = empty_acts_result[0].get('count', 0) if empty_acts_result else 0
            
            return {
                'total_acts': total_acts,
                'total_articles': total_articles,
                'total_relationships': total_relationships,
                'orphaned_articles': orphaned_articles,
                'empty_acts': empty_acts,
                'relationship_integrity': (total_articles - orphaned_articles) / total_articles if total_articles > 0 else 0,
                'act_completeness': (total_acts - empty_acts) / total_acts if total_acts > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Relationship validation failed: {e}")
            raise
    
    async def get_entity_statistics(self) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        try:
            # Acts by year
            acts_by_year_query = """
                MATCH (a:Act) WHERE a.source = 'vlaamse'
                RETURN date(a.date).year as year, count(a) as count
                ORDER BY year DESC
                LIMIT 10
            """
            acts_by_year = await self.execute_cypher(acts_by_year_query)
            
            # Articles per act statistics
            articles_per_act_query = """
                MATCH (a:Act)-[:HAS_ARTICLE]->(art:Article)
                WHERE a.source = 'vlaamse'
                WITH a, count(art) as article_count
                RETURN 
                    min(article_count) as min_articles,
                    max(article_count) as max_articles,
                    avg(article_count) as avg_articles,
                    count(a) as acts_with_articles
            """
            articles_stats = await self.execute_cypher(articles_per_act_query)
            
            # Sample acts with titles
            sample_acts_query = """
                MATCH (a:Act) WHERE a.source = 'vlaamse'
                RETURN a.id, a.title, a.date
                ORDER BY a.date DESC
                LIMIT 5
            """
            sample_acts = await self.execute_cypher(sample_acts_query)
            
            return {
                'acts_by_year': [
                    {'year': result.get('year'), 'count': result.get('count')}
                    for result in acts_by_year
                ],
                'articles_statistics': articles_stats[0].data if articles_stats else {},
                'sample_acts': [
                    {
                        'id': result.get('a.id'),
                        'title': result.get('a.title'),
                        'date': str(result.get('a.date'))
                    }
                    for result in sample_acts
                ]
            }
            
        except Exception as e:
            logger.error(f"Entity statistics failed: {e}")
            raise
    
    async def find_entities_by_content(self, search_term: str, limit: int = 10) -> List[Neo4jResult]:
        """
        Find entities containing specific content.
        
        Args:
            search_term: Term to search for in text content
            limit: Maximum number of results
            
        Returns:
            List of matching entities
        """
        try:
            query = """
                MATCH (art:Article) 
                WHERE art.id STARTS WITH 'vlaamse_' 
                AND (art.text CONTAINS $search_term OR art.heading CONTAINS $search_term)
                OPTIONAL MATCH (a:Act)-[:HAS_ARTICLE]->(art)
                RETURN art.id, art.number, art.heading, a.id as act_id, a.title as act_title
                LIMIT $limit
            """
            
            return await self.execute_cypher(query, {
                'search_term': search_term,
                'limit': limit
            })
            
        except Exception as e:
            logger.error(f"Content search failed: {e}")
            raise
    
    async def verify_entity_existence(self, entity_ids: List[str]) -> Dict[str, bool]:
        """
        Verify that specific entity IDs exist in Neo4j.
        
        Args:
            entity_ids: List of act or article IDs to verify
            
        Returns:
            Dictionary mapping entity IDs to existence status
        """
        try:
            results = {}
            
            for entity_id in entity_ids:
                # Check if it's an act
                act_query = "MATCH (a:Act {id: $entity_id}) RETURN count(a) as count"
                act_result = await self.execute_cypher(act_query, {'entity_id': entity_id})
                act_exists = act_result[0].get('count', 0) > 0 if act_result else False
                
                # Check if it's an article
                article_query = "MATCH (art:Article {id: $entity_id}) RETURN count(art) as count"
                article_result = await self.execute_cypher(article_query, {'entity_id': entity_id})
                article_exists = article_result[0].get('count', 0) > 0 if article_result else False
                
                results[entity_id] = act_exists or article_exists
            
            return results
            
        except Exception as e:
            logger.error(f"Entity existence verification failed: {e}")
            raise
    
    def close(self):
        """Close the Neo4j driver connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j connection closed")
