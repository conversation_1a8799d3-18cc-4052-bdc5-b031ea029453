#!/usr/bin/env python3
"""
Comprehensive tests for registry deduplication fixes.

Tests the new atomic reservation system and state management to ensure
duplicate document processing is properly prevented.
"""

import pytest
import uuid
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from concurrent.futures import Thr<PERSON>PoolExecutor, as_completed

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from common.registry import GlobalRegistry, RegistryEntry, calculate_sha256
from common.transaction import TransactionalIngester
from common.models import CommonAct, CommonArticle


class TestRegistryDeduplication:
    """Test registry deduplication functionality."""
    
    @pytest.fixture
    def mock_registry(self):
        """Create a mock registry for testing."""
        with patch('common.registry.create_client') as mock_create_client:
            mock_client = Mock()
            mock_create_client.return_value = mock_client

            registry = GlobalRegistry("http://test.supabase.co", "test_key", auto_cleanup=False)
            registry.client = mock_client

            # Mock table operations with proper chaining
            mock_table = Mock()
            mock_client.table.return_value = mock_table

            # Setup default mock chain for select operations
            mock_select = Mock()
            mock_eq = Mock()
            mock_execute = Mock()

            mock_table.select.return_value = mock_select
            mock_select.eq.return_value = mock_eq
            mock_eq.execute.return_value = mock_execute

            # Setup default mock chain for insert operations
            mock_insert = Mock()
            mock_insert_execute = Mock()
            mock_table.insert.return_value = mock_insert
            mock_insert.execute.return_value = mock_insert_execute

            # Setup default mock chain for update operations
            mock_update = Mock()
            mock_update_eq1 = Mock()
            mock_update_eq2 = Mock()
            mock_update_eq3 = Mock()
            mock_update_execute = Mock()

            mock_table.update.return_value = mock_update
            mock_update.eq.return_value = mock_update_eq1
            mock_update_eq1.eq.return_value = mock_update_eq2
            mock_update_eq2.eq.return_value = mock_update_eq3
            mock_update_eq3.execute.return_value = mock_update_execute

            return registry, mock_client, mock_table
    
    def test_atomic_reservation_success(self, mock_registry):
        """Test successful atomic document reservation."""
        registry, mock_client, mock_table = mock_registry

        # Mock no existing document
        mock_table.select.return_value.eq.return_value.execute.return_value.data = []

        # Mock successful insert
        mock_table.insert.return_value.execute.return_value.data = [{"id": "test_id"}]

        # Test reservation
        processing_id = str(uuid.uuid4())
        result = registry.try_reserve_document("vlaamse", "test_doc", "abc123", processing_id)

        assert result is True
        mock_table.insert.assert_called_once()

        # Verify the data passed to insert
        insert_call = mock_table.insert.call_args[0][0]
        assert insert_call["source"] == "vlaamse"
        assert insert_call["doc_id"] == "test_doc"
        assert insert_call["sha256"] == "abc123"
        assert insert_call["processing_status"] == "RESERVED"
        assert insert_call["processing_id"] == processing_id
    
    def test_atomic_reservation_already_exists(self, mock_registry):
        """Test reservation fails when document already exists and is completed."""
        registry, mock_client, mock_table = mock_registry
        
        # Mock existing completed document
        mock_table.select.return_value.eq.return_value.execute.return_value.data = [{
            "id": "existing_id",
            "source": "vlaamse",
            "doc_id": "test_doc",
            "sha256": "abc123",
            "processing_status": "COMPLETED",
            "neo4j_loaded": True,
            "pinecone_loaded": True
        }]
        
        # Test reservation
        result = registry.try_reserve_document("vlaamse", "test_doc", "abc123")
        
        assert result is False
        mock_table.insert.assert_not_called()
        mock_table.update.assert_not_called()
    
    def test_atomic_reservation_currently_processing(self, mock_registry):
        """Test reservation fails when document is currently being processed."""
        registry, mock_client, mock_table = mock_registry
        
        # Mock existing document being processed
        mock_table.select.return_value.eq.return_value.execute.return_value.data = [{
            "id": "existing_id",
            "source": "vlaamse",
            "doc_id": "test_doc",
            "sha256": "abc123",
            "processing_status": "PROCESSING",
            "neo4j_loaded": False,
            "pinecone_loaded": False
        }]
        
        # Test reservation
        result = registry.try_reserve_document("vlaamse", "test_doc", "abc123")
        
        assert result is False
        mock_table.insert.assert_not_called()
        mock_table.update.assert_not_called()
    
    def test_atomic_reservation_failed_state_retry(self, mock_registry):
        """Test reservation succeeds when document is in FAILED state."""
        registry, mock_client, mock_table = mock_registry
        
        # Mock existing failed document
        mock_table.select.return_value.eq.return_value.execute.return_value.data = [{
            "id": "existing_id",
            "source": "vlaamse",
            "doc_id": "test_doc",
            "sha256": "abc123",
            "processing_status": "FAILED",
            "neo4j_loaded": False,
            "pinecone_loaded": False
        }]
        
        # Mock successful update
        mock_table.update.return_value.eq.return_value.eq.return_value.eq.return_value.execute.return_value.data = [{"id": "existing_id"}]
        
        # Test reservation
        processing_id = str(uuid.uuid4())
        result = registry.try_reserve_document("vlaamse", "test_doc", "abc123", processing_id)
        
        assert result is True
        mock_table.update.assert_called_once()
    
    def test_sha256_mismatch_allows_reprocessing(self, mock_registry):
        """Test that SHA-256 mismatch allows reprocessing of document."""
        registry, mock_client, mock_table = mock_registry
        
        # Mock existing document with different SHA-256
        mock_table.select.return_value.eq.return_value.execute.return_value.data = [{
            "id": "existing_id",
            "source": "vlaamse",
            "doc_id": "test_doc",
            "sha256": "old_hash",
            "processing_status": "COMPLETED",
            "neo4j_loaded": True,
            "pinecone_loaded": True
        }]
        
        # Mock successful update
        mock_table.update.return_value.eq.return_value.eq.return_value.execute.return_value.data = [{"id": "existing_id"}]
        
        # Test reservation with new SHA-256
        processing_id = str(uuid.uuid4())
        result = registry.try_reserve_document("vlaamse", "test_doc", "new_hash", processing_id)
        
        assert result is True
        mock_table.update.assert_called_once()
        
        # Verify the update resets the document for reprocessing
        update_call = mock_table.update.call_args[0][0]
        assert update_call["sha256"] == "new_hash"
        assert update_call["processing_status"] == "RESERVED"
        assert update_call["neo4j_loaded"] is False
        assert update_call["pinecone_loaded"] is False
    
    def test_exists_method_considers_processing_status(self, mock_registry):
        """Test that exists method considers processing status."""
        registry, mock_client, mock_table = mock_registry
        
        # Test 1: Document being processed should return True (exists)
        mock_table.select.return_value.eq.return_value.eq.return_value.execute.return_value.data = [{
            "source": "vlaamse",
            "doc_id": "test_doc",
            "sha256": "abc123",
            "processing_status": "PROCESSING",
            "neo4j_loaded": False,
            "pinecone_loaded": False
        }]
        
        result = registry.exists("vlaamse", "test_doc", "abc123")
        assert result is True
        
        # Test 2: Completed document should return True
        mock_table.select.return_value.eq.return_value.eq.return_value.execute.return_value.data = [{
            "source": "vlaamse",
            "doc_id": "test_doc",
            "sha256": "abc123",
            "processing_status": "COMPLETED",
            "neo4j_loaded": True,
            "pinecone_loaded": True
        }]
        
        result = registry.exists("vlaamse", "test_doc", "abc123")
        assert result is True
        
        # Test 3: Failed document should return False
        mock_table.select.return_value.eq.return_value.eq.return_value.execute.return_value.data = [{
            "source": "vlaamse",
            "doc_id": "test_doc",
            "sha256": "abc123",
            "processing_status": "FAILED",
            "neo4j_loaded": False,
            "pinecone_loaded": False
        }]
        
        result = registry.exists("vlaamse", "test_doc", "abc123")
        assert result is False
    
    def test_cleanup_stale_reservations(self, mock_registry):
        """Test cleanup of stale reservations."""
        registry, mock_client, mock_table = mock_registry
        
        # Mock stale reservations
        old_time = (datetime.now() - timedelta(hours=1)).isoformat()
        mock_table.select.return_value.in_.return_value.lt.return_value.execute.return_value.data = [
            {
                "id": "stale_1",
                "source": "vlaamse",
                "doc_id": "stale_doc_1",
                "processing_status": "RESERVED",
                "reserved_at": old_time
            },
            {
                "id": "stale_2", 
                "source": "vlaamse",
                "doc_id": "stale_doc_2",
                "processing_status": "PROCESSING",
                "reserved_at": old_time
            }
        ]
        
        # Mock successful updates
        mock_table.update.return_value.eq.return_value.execute.return_value.data = [{"id": "updated"}]
        
        # Test cleanup
        cleaned_count = registry.cleanup_stale_reservations(max_age_minutes=30)
        
        assert cleaned_count == 2
        assert mock_table.update.call_count == 2
    
    @patch('common.registry.GlobalRegistry')
    def test_transactional_ingester_uses_atomic_reservation(self, mock_registry_class):
        """Test that TransactionalIngester uses the new atomic reservation system."""
        # Mock registry instance
        mock_registry = Mock()
        mock_registry_class.return_value = mock_registry
        
        # Mock successful reservation
        mock_registry.try_reserve_document.return_value = True
        
        # Create ingester
        ingester = TransactionalIngester(
            source="vlaamse",
            dry_run=True,  # Use dry run to avoid external dependencies
            use_registry=True
        )
        
        # Create test data
        act = CommonAct(
            id="test_act",
            title="Test Act",
            date=datetime.now().date(),
            language="nl",
            source="vlaamse"
        )
        articles = [
            CommonArticle(
                id="test_article",
                act_id="test_act",
                title="Test Article",
                content="Test content",
                language="nl",
                source="vlaamse"
            )
        ]
        
        # Test ingestion
        result = ingester.ingest_document_atomic(act, articles, raw_content="<xml>test</xml>")
        
        # Verify reservation was attempted
        mock_registry.try_reserve_document.assert_called_once()
        call_args = mock_registry.try_reserve_document.call_args
        assert call_args[0][0] == "vlaamse"  # source
        assert call_args[0][1] == "test_act"  # doc_id
        assert call_args[0][2] is not None   # sha256
        assert call_args[0][3] is not None   # processing_id


    def test_concurrent_processing_prevention(self, mock_registry):
        """Test that concurrent processing of the same document is prevented."""
        registry, mock_client, mock_table = mock_registry

        # Simulate race condition scenario
        reservation_attempts = []
        processing_id_1 = str(uuid.uuid4())
        processing_id_2 = str(uuid.uuid4())

        def mock_select_response(*args, **kwargs):
            """Mock select response that simulates timing."""
            # First call: no existing document
            # Second call: document already reserved by first process
            if len(reservation_attempts) == 0:
                return Mock(data=[])
            else:
                return Mock(data=[{
                    "id": "existing_id",
                    "source": "vlaamse",
                    "doc_id": "test_doc",
                    "processing_status": "RESERVED",
                    "processing_id": processing_id_1
                }])

        def mock_insert_response(*args, **kwargs):
            """Mock insert that only succeeds for first attempt."""
            reservation_attempts.append(len(reservation_attempts))
            if len(reservation_attempts) == 1:
                return Mock(data=[{"id": "new_id"}])
            else:
                # Simulate unique constraint violation
                from supabase.client import APIError
                raise APIError("duplicate key value violates unique constraint")

        # Setup mocks
        mock_table.select.return_value.eq.return_value.execute.side_effect = mock_select_response
        mock_table.insert.return_value.execute.side_effect = mock_insert_response

        # Test concurrent reservations
        result_1 = registry.try_reserve_document("vlaamse", "test_doc", "abc123", processing_id_1)
        result_2 = registry.try_reserve_document("vlaamse", "test_doc", "abc123", processing_id_2)

        # Only first reservation should succeed
        assert result_1 is True
        assert result_2 is False
        assert len(reservation_attempts) == 2


class TestConcurrentProcessingIntegration:
    """Integration tests for concurrent processing scenarios."""

    def test_multiple_threads_same_document(self):
        """Test multiple threads trying to process the same document."""
        # This test would require a real database connection
        # For now, we'll create a mock-based simulation

        with patch('common.registry.create_client') as mock_create_client:
            mock_client = Mock()
            mock_create_client.return_value = mock_client

            # Shared state to track reservations
            reserved_documents = set()
            reservation_lock = threading.Lock()

            def mock_try_reserve(source, doc_id, sha256, processing_id):
                """Mock reservation that simulates database behavior."""
                with reservation_lock:
                    key = f"{source}:{doc_id}"
                    if key in reserved_documents:
                        return False
                    reserved_documents.add(key)
                    return True

            # Create registry with mocked reservation
            registry = GlobalRegistry("http://test.supabase.co", "test_key", auto_cleanup=False)
            registry.try_reserve_document = mock_try_reserve

            # Test concurrent reservations
            results = []

            def attempt_reservation(thread_id):
                """Attempt to reserve a document."""
                processing_id = str(uuid.uuid4())
                result = registry.try_reserve_document("vlaamse", "test_doc", "abc123", processing_id)
                results.append((thread_id, result, processing_id))
                return result

            # Run multiple threads
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(attempt_reservation, i) for i in range(5)]

                # Wait for all to complete
                for future in as_completed(futures):
                    future.result()

            # Only one thread should have succeeded
            successful_reservations = [r for r in results if r[1] is True]
            assert len(successful_reservations) == 1

            # All others should have failed
            failed_reservations = [r for r in results if r[1] is False]
            assert len(failed_reservations) == 4


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
